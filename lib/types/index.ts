

export interface PendingUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  birthDate: Date;
  role: 'doctor' | 'assistant';
  clinic: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Patient {
  id: string;
  doctorId: string;
  firstName: string;
  lastName: string;
  email?: string;
  tcKimlik: string;
  phone: string;
  gender: string;
  birthDate: Date;
  yas?: number;
  address?: string;
  totalProcedures?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface IstemFormuData {
  id?: string;
  patientId: string;
  userId: string;
  diagnosis: string;
  notes: string;
  images?: string[];
  xrayTypes: string[];
  bitewingSides: string[];
  selectedTeeth?: string;
  paymentResponsible: 'clinic' | 'patient';
  priorityStatus: 'Normal' | 'Acil' | 'Çok Acil' | '<PERSON>ü<PERSON>ük';
  status: 'pending' | 'completed' | 'draft';
  createdAt: Date;
  completedAt?: Date;
}

export interface PatientImage {
  id?: string;
  patientId: string;
  uploadedBy: string; // userId who uploaded
  imageUrl: string;
  imagePath: string; // storage path for deletion
  filename: string;
  scanType?: string;
  notes?: string;
  createdAt: Date;
}