import { db } from '../firebase';
import {
  collection,
  addDoc,
  getDocs,
  doc,
  deleteDoc,
  query,
  where,
  orderBy,
} from 'firebase/firestore';

export interface PatientImage {
  id?: string;
  patientId: string;
  uploadedBy: string; // userId who uploaded
  imageUrl: string;
  imagePath: string; // storage path for deletion
  filename: string;
  scanType?: string;
  notes?: string;
  createdAt: Date;
}

/**
 * Add a standalone patient image record
 */
export const addPatientImage = async (imageData: Omit<PatientImage, 'id' | 'createdAt'>): Promise<string> => {
  try {
    const patientImagesCollection = collection(db, 'patient-images');

    const processedData: Omit<PatientImage, 'id'> = {
      ...imageData,
      createdAt: new Date(),
    };

    const docRef = await addDoc(patientImagesCollection, processedData);
    return docRef.id;
  } catch (error) {
    console.error('Error adding patient image: ', error);
    throw new Error('Failed to add patient image');
  }
};

/**
 * Get all standalone images for a specific patient
 */
export const getPatientImages = async (patientId: string): Promise<PatientImage[]> => {
  try {
    const patientImagesCollection = collection(db, 'patient-images');
    const imagesQuery = query(
      patientImagesCollection,
      where('patientId', '==', patientId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(imagesQuery);
    const images = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date()
    })) as PatientImage[];
    
    return images;
  } catch (error) {
    console.error('Error fetching patient images: ', error);
    throw new Error('Failed to fetch patient images');
  }
};

/**
 * Delete a patient image record
 */
export const deletePatientImage = async (imageId: string): Promise<void> => {
  try {
    const docRef = doc(db, 'patient-images', imageId);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting patient image:', error);
    throw new Error('Failed to delete patient image');
  }
};

/**
 * Get images by upload user (for admin purposes)
 */
export const getImagesByUploader = async (uploaderId: string): Promise<PatientImage[]> => {
  try {
    const patientImagesCollection = collection(db, 'patient-images');
    const imagesQuery = query(
      patientImagesCollection,
      where('uploadedBy', '==', uploaderId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(imagesQuery);
    const images = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date()
    })) as PatientImage[];
    
    return images;
  } catch (error) {
    console.error('Error fetching images by uploader: ', error);
    throw new Error('Failed to fetch images by uploader');
  }
};
